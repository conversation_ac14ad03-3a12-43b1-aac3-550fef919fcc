import XCTest
@testable import IngredientScanner

@MainActor
final class SimpleMealPlanTests: XCTestCase {

    // MARK: - Basic Fingerprint Tests

    func test_fingerprintGeneration_basic() {
        // Given
        let config = CustomConfiguration(
            selectedMeals: [.breakfast],
            mealConfigurations: [.breakfast: MealConfig(cookingTimeMinutes: 30, numberOfDishes: 2)],
            days: 1,
            cuisines: ["Italian"],
            additionalRequest: "Test"
        )
        let startDate = Date()
        let equipment = ["Oven"]

        // When
        let fingerprint = GenerationFingerprint.current(
            from: config,
            startDate: startDate,
            equipment: equipment
        )

        // Then
        XCTAssertEqual(fingerprint.days, 1)
        XCTAssertEqual(fingerprint.mealConfigs.count, 1)
        XCTAssertEqual(fingerprint.cuisines, ["Italian"])
        XCTAssertEqual(fingerprint.additionalRequest, "Test")
    }

    func test_fingerprintComparison_detectsChanges() {
        // Given
        let config1 = CustomConfiguration(
            selectedMeals: [.breakfast],
            mealConfigurations: [.breakfast: MealConfig(cookingTimeMinutes: 30, numberOfDishes: 1)],
            days: 1,
            cuisines: ["Italian"],
            additionalRequest: "Test"
        )

        let config2 = CustomConfiguration(
            selectedMeals: [.breakfast],
            mealConfigurations: [.breakfast: MealConfig(cookingTimeMinutes: 30, numberOfDishes: 2)], // Changed
            days: 1,
            cuisines: ["Italian"],
            additionalRequest: "Test"
        )

        let startDate = Date()
        let equipment = ["Oven"]

        // When
        let fingerprint1 = GenerationFingerprint.current(from: config1, startDate: startDate, equipment: equipment)
        let fingerprint2 = GenerationFingerprint.current(from: config2, startDate: startDate, equipment: equipment)

        // Then
        XCTAssertNotEqual(fingerprint1, fingerprint2, "Fingerprints should differ when configuration changes")
    }

    func test_fingerprintComparison_identicalMatch() {
        // Given
        let config = CustomConfiguration(
            selectedMeals: [.breakfast],
            mealConfigurations: [.breakfast: MealConfig(cookingTimeMinutes: 30, numberOfDishes: 2)],
            days: 1,
            cuisines: ["Italian"],
            additionalRequest: "Test"
        )
        let startDate = Date()
        let equipment = ["Oven"]

        // When
        let fingerprint1 = GenerationFingerprint.current(from: config, startDate: startDate, equipment: equipment)
        let fingerprint2 = GenerationFingerprint.current(from: config, startDate: startDate, equipment: equipment)

        // Then
        XCTAssertEqual(fingerprint1, fingerprint2, "Identical configurations should produce equal fingerprints")
    }

    // MARK: - Basic Prefetch Tests

    func test_prefetchFlag_mealPlanDisabled() async throws {
        // Given
        let mockService = SimpleMockRecipeService()

        // When - Call with prefetchDetails: false (Meal Plan path)
        try await mockService.prefetchRecipeDetails(for: [], preferences: RecipePreferences(from: UserPreferences(), cookingTime: 30))

        // Then
        XCTAssertTrue(mockService.prefetchWasCalled, "Prefetch method should be called")
    }

    func test_prefetchFlag_quickEnabled() async throws {
        // Given
        let mockService = SimpleMockRecipeService()

        // When - Call prefetch method directly
        try await mockService.prefetchRecipeDetails(for: [], preferences: RecipePreferences(from: UserPreferences(), cookingTime: 30))

        // Then
        XCTAssertTrue(mockService.prefetchWasCalled, "Prefetch should be called when enabled")
    }

    // MARK: - Basic Date Tests

    func test_dateIndexing_utcFormat() {
        // Given
        let date = Date()
        
        // When
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone(identifier: "UTC")
        formatter.locale = Locale(identifier: "en_US_POSIX")
        let dateString = formatter.string(from: date)
        
        // Then
        XCTAssertTrue(dateString.matches("\\d{4}-\\d{2}-\\d{2}"), "Date should be in yyyy-MM-dd format")
    }

    // MARK: - Basic Timeout Tests

    func test_timeoutHelper_boundedNanoseconds() {
        // Given
        let seconds: TimeInterval = 5.0
        
        // When
        let nanoseconds = UInt64(seconds * 1_000_000_000)
        
        // Then
        XCTAssertEqual(nanoseconds, 5_000_000_000, "Should convert seconds to nanoseconds correctly")
        XCTAssertLessThanOrEqual(nanoseconds, UInt64.max, "Should not overflow")
    }
}

// MARK: - Simple Mock Classes

class SimpleMockRecipeService: RecipeGenerationServiceProtocol {
    var prefetchWasCalled = false
    
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        return []
    }
    
    func generateRecipeDetails(for idea: RecipeIdea, preferences: RecipePreferences) async throws -> Recipe {
        return Recipe(
            recipeTitle: "Mock Recipe",
            description: "Mock Description",
            ingredients: [],
            instructions: [],
            nutrition: Recipe.NutritionInfo(calories: 300, protein: 20, carbs: 30, fat: 10),
            cookingTime: "30 minutes",
            servings: 4,
            difficulty: .easy
        )
    }
    
    func prefetchRecipeDetails(for ideas: [RecipeIdea], preferences: RecipePreferences) async throws {
        prefetchWasCalled = true
    }
    
    func generateMealPlan(
        from ingredients: [String],
        preferences: RecipePreferences,
        mealTypes: [MealType],
        days: Int,
        servings: Int
    ) async throws -> MealPlan {
        return MealPlan(days: [])
    }
}



extension String {
    func matches(_ regex: String) -> Bool {
        return self.range(of: regex, options: .regularExpression, range: nil, locale: nil) != nil
    }
}
